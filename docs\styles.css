:root {
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #8b5cf6;
    --background: #0f172a;
    --surface: #1e293b;
    --surface-light: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #475569;
    --gradient: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
    --gradient-alt: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
    --gradient-bright: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #22d3ee 100%);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.5rem;
}

.logo {
    width: 32px;
    height: 32px;
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.4));
}

.brand-text {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--text-primary);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}



/* Hero Section */
.hero {
    padding: 8rem 0 4rem;
    background: 
        radial-gradient(circle at 20% 50%, rgba(37, 99, 235, 0.1) 0%, transparent 70%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 70%),
        var(--background);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="%23334155" stroke-width="1" opacity="0.3"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.hero-title:hover {
    transform: translateY(-2px);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.hero-title-main {
    display: block;
    color: var(--text-primary);
    font-size: 1.2em;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
    position: relative;
}

.hero-title-main::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    width: 60px;
    height: 3px;
    background: var(--gradient);
    border-radius: 2px;
    transform: translateX(-50%);
    opacity: 0.8;
}


@keyframes sparkle {
    0%, 100% {
        opacity: 0.5;
        transform: scale(0.8) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
}

.gradient-text {
    background: var(--gradient-bright);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    position: relative;
    animation: gradientShift 6s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: var(--gradient-bright);
        -webkit-background-clip: text;
        background-clip: text;
    }
    50% {
        background: linear-gradient(135deg, #22d3ee 0%, #60a5fa 50%, #a78bfa 100%);
        -webkit-background-clip: text;
        background-clip: text;
    }
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--gradient);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--surface);
    transform: translateY(-2px);
}

/* Hero Demo */
.hero-demo {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.terminal {
    background: var(--surface);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.terminal-header {
    background: var(--surface-light);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.terminal-controls {
    display: flex;
    gap: 0.5rem;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.terminal-title {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.terminal-body {
    padding: 1.5rem;
}

.terminal-body pre {
    background: transparent;
    color: var(--text-primary);
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    overflow-x: auto;
}

/* Sections */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient);
    border-radius: 2px;
}



/* Features Section */
.features {
    padding: 6rem 0;
    background: var(--surface);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--surface);
    padding: 2rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    display: inline-block;
}

.external-icon {
    font-size: 0.875rem;
    margin-left: 0.25rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.external-icon:hover {
    opacity: 1;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Installation Section */
.installation {
    padding: 6rem 0;
}

.installation-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.install-option {
    background: var(--surface);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid var(--border-color);
}

.install-option h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.code-block {
    position: relative;
    background: var(--background);
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.code-block pre {
    padding: 1.5rem;
    margin: 0;
    overflow-x: auto;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.copy-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--surface-light);
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: var(--surface);
    color: var(--text-primary);
}

/* Usage Section */
.usage {
    padding: 6rem 0;
    background: var(--surface);
}

.usage-tabs {
    max-width: 1000px;
    margin: 0 auto;
}

.tab-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    background: var(--background);
    padding: 0.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-color);
    color: white;
}

.tab-content {
    display: none;
    background: var(--background);
    border-radius: 1rem;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.tab-content.active {
    display: block;
}

.code-example {
    padding: 0;
}

.code-example pre {
    padding: 2rem;
    margin: 0;
    overflow-x: auto;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    background: transparent;
}

/* Examples Section */
.examples {
    padding: 6rem 0;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.example-card {
    background: var(--surface);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.example-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.example-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.example-card ul {
    list-style: none;
}

.example-card li {
    color: var(--text-secondary);
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.example-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Footer */
.footer {
    background: var(--surface);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem;
    margin-top: 6rem;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--text-primary);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    position: relative;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, transparent 0%, rgba(59, 130, 246, 0.1) 100%);
}

.footer-bottom a:hover {
    color: var(--text-primary);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.footer-bottom a::before {
    margin-right: 0.5rem;
    font-size: 0.875em;
}

.footer-bottom a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.footer-bottom a:hover::after {
    width: 100%;
}

/* Recording Section */
.recording {
    padding: 6rem 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    position: relative;
}

.recording .section-subtitle {
    text-align: center;
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 3rem;
}

.recording-content {
    display: grid;
    gap: 4rem;
}

.recording-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.recording-feature {
    text-align: center;
    padding: 2rem;
    background: var(--surface);
    border-radius: 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.recording-feature:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.recording-feature .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.recording-feature h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.recording-feature p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.recording-example {
    background: var(--surface);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.recording-example h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.25rem;
}

.recording-use-cases h3 {
    margin-bottom: 2rem;
    text-align: center;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.use-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.use-case {
    background: var(--surface);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.use-case:hover {
    transform: translateY(-3px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.use-case h4 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.use-case p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}



/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        gap: 1rem;
    }
    
    .hero {
        padding: 6rem 0 2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .installation-options {
        grid-template-columns: 1fr;
    }
    
    .tab-buttons {
        flex-direction: column;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card,
.example-card,
.install-option {
    animation: fadeInUp 0.6s ease forwards;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Code Syntax Highlighting */
.hljs {
    background: transparent !important;
    color: var(--text-primary) !important;
}

.hljs-keyword {
    color: #ff79c6 !important;
    font-weight: bold;
}

.hljs-string {
    color: #f1fa8c !important;
}

.hljs-comment {
    color: var(--text-muted) !important;
    font-style: italic;
}

.hljs-number {
    color: #bd93f9 !important;
}

.hljs-function {
    color: #50fa7b !important;
}

.hljs-type {
    color: #8be9fd !important;
}
